-- Migration: Add Package Enhancements
-- Description: Adds support for pet size-based pricing, custom business options, and pet type selection
-- Date: 2025-07-21

-- Create package_size_pricing table for size-based pricing
CREATE TABLE IF NOT EXISTS `package_size_pricing` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `package_id` int(11) NOT NULL,
  `size_category` enum('small','medium','large','extra_large') NOT NULL,
  `weight_range_min` decimal(8,2) DEFAULT NULL,
  `weight_range_max` decimal(8,2) DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_package_size_pricing_package` (`package_id`),
  KEY `idx_package_size_pricing_size` (`size_category`),
  CONSTRAINT `fk_package_size_pricing_package` FOREIGN KEY (`package_id`) REFERENCES `service_packages` (`package_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Add new columns to service_packages table to support enhanced features
ALTER TABLE `service_packages` 
ADD COLUMN IF NOT EXISTS `has_size_pricing` tinyint(1) DEFAULT 0 AFTER `delivery_fee_per_km`,
ADD COLUMN IF NOT EXISTS `uses_custom_options` tinyint(1) DEFAULT 0 AFTER `has_size_pricing`;

-- Insert default pet types for existing providers
INSERT IGNORE INTO `business_pet_types` (`provider_id`, `pet_type`, `is_active`)
SELECT `provider_id`, 'Dogs', 1 FROM `service_providers` WHERE `provider_type` = 'cremation'
UNION ALL
SELECT `provider_id`, 'Cats', 1 FROM `service_providers` WHERE `provider_type` = 'cremation'
UNION ALL
SELECT `provider_id`, 'Birds', 1 FROM `service_providers` WHERE `provider_type` = 'cremation'
UNION ALL
SELECT `provider_id`, 'Rabbits', 1 FROM `service_providers` WHERE `provider_type` = 'cremation';

-- Insert default custom options for existing providers
INSERT IGNORE INTO `business_custom_options` (`provider_id`, `option_type`, `option_value`, `is_active`)
SELECT `provider_id`, 'category', 'Private', 1 FROM `service_providers` WHERE `provider_type` = 'cremation'
UNION ALL
SELECT `provider_id`, 'category', 'Communal', 1 FROM `service_providers` WHERE `provider_type` = 'cremation'
UNION ALL
SELECT `provider_id`, 'cremation_type', 'Standard', 1 FROM `service_providers` WHERE `provider_type` = 'cremation'
UNION ALL
SELECT `provider_id`, 'cremation_type', 'Premium', 1 FROM `service_providers` WHERE `provider_type` = 'cremation'
UNION ALL
SELECT `provider_id`, 'cremation_type', 'Deluxe', 1 FROM `service_providers` WHERE `provider_type` = 'cremation'
UNION ALL
SELECT `provider_id`, 'processing_time', '1-2 days', 1 FROM `service_providers` WHERE `provider_type` = 'cremation'
UNION ALL
SELECT `provider_id`, 'processing_time', '2-3 days', 1 FROM `service_providers` WHERE `provider_type` = 'cremation'
UNION ALL
SELECT `provider_id`, 'processing_time', '3-5 days', 1 FROM `service_providers` WHERE `provider_type` = 'cremation';
