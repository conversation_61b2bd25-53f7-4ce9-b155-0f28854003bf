'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Modal } from '@/components/ui';
import { XMarkIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import { useToast } from '@/context/ToastContext';
import { ToastType } from '@/types/toast';

// Types
interface AddOn {
  name: string;
  price: number | null;
}

interface PackageFormData {
  name: string;
  description: string;
  category: string;
  cremationType: string;
  processingTime: string;
  price: number;
  deliveryFeePerKm: number;
  inclusions: string[];
  addOns: AddOn[];
  conditions: string;
  images: string[];
  packageId?: number;
  // Enhanced features
  pricePerKg: number;
  usesCustomOptions: boolean;
  customCategories: string[];
  customCremationTypes: string[];
  customProcessingTimes: string[];
  supportedPetTypes: string[];
}

interface PackageModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  mode: 'create' | 'edit';
  packageId?: number;
  initialData?: Partial<PackageFormData>;
}

const PackageModal: React.FC<PackageModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  mode,
  packageId,
  initialData
}) => {
  const { showToast } = useToast();
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  
  // Form state
  const [formData, setFormData] = useState<PackageFormData>({
    name: '',
    description: '',
    category: 'Private',
    cremationType: 'Standard',
    processingTime: '1-2 days',
    price: 0,
    deliveryFeePerKm: 0,
    inclusions: [],
    addOns: [],
    conditions: '',
    images: [],
    packageId: packageId,
    // Enhanced features
    pricePerKg: 0,
    usesCustomOptions: false,
    customCategories: [],
    customCremationTypes: [],
    customProcessingTimes: [],
    supportedPetTypes: ['Dogs', 'Cats', 'Birds', 'Rabbits']
  });

  // UI state
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  
  // Form field states
  const [newInclusion, setNewInclusion] = useState('');
  const [newAddOn, setNewAddOn] = useState('');
  const [newAddOnPrice, setNewAddOnPrice] = useState<string>('');
  const [uploadingImages, setUploadingImages] = useState<Set<string>>(new Set());
  
  // Enhanced features states
  const [newCustomCategory, setNewCustomCategory] = useState('');
  const [newCustomCremationType, setNewCustomCremationType] = useState('');
  const [newCustomProcessingTime, setNewCustomProcessingTime] = useState('');

  // Image upload states
  const [uploadingImages, setUploadingImages] = useState<Set<string>>(new Set());
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load package data for edit mode
  useEffect(() => {
    if (mode === 'edit' && packageId && isOpen) {
      loadPackageData();
    } else if (mode === 'create' && isOpen) {
      resetForm();
    }
  }, [mode, packageId, isOpen]);

  // Apply initial data if provided
  useEffect(() => {
    if (initialData && isOpen) {
      setFormData(prev => ({ ...prev, ...initialData }));
    }
  }, [initialData, isOpen]);

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      category: 'Private',
      cremationType: 'Standard',
      processingTime: '1-2 days',
      price: 0,
      deliveryFeePerKm: 0,
      inclusions: [],
      addOns: [],
      conditions: '',
      images: [],
      packageId: undefined,
      pricePerKg: 0,
      usesCustomOptions: false,
      customCategories: [],
      customCremationTypes: [],
      customProcessingTimes: [],
      supportedPetTypes: ['Dogs', 'Cats', 'Birds', 'Rabbits']
    });
    setErrors({});
    setNewInclusion('');
    setNewAddOn('');
    setNewAddOnPrice('');
    setNewCustomCategory('');
    setNewCustomCremationType('');
    setNewCustomProcessingTime('');
  };

  const loadPackageData = async () => {
    if (!packageId) return;
    
    setIsLoading(true);
    try {
      const response = await fetch(`/api/packages/${packageId}`, {
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error('Failed to load package data');
      }
      
      const data = await response.json();
      const pkg = data.package;
      
      setFormData({
        name: pkg.name || '',
        description: pkg.description || '',
        category: pkg.category || 'Private',
        cremationType: pkg.cremationType || 'Standard',
        processingTime: pkg.processingTime || '1-2 days',
        price: pkg.price || 0,
        deliveryFeePerKm: pkg.deliveryFeePerKm || 0,
        inclusions: pkg.inclusions || [],
        addOns: pkg.addOns || [],
        conditions: pkg.conditions || '',
        images: pkg.images || [],
        packageId: pkg.id,
        pricePerKg: pkg.pricePerKg || 0,
        usesCustomOptions: pkg.usesCustomOptions || false,
        customCategories: pkg.customCategories || [],
        customCremationTypes: pkg.customCremationTypes || [],
        customProcessingTimes: pkg.customProcessingTimes || [],
        supportedPetTypes: pkg.supportedPetTypes || ['Dogs', 'Cats', 'Birds', 'Rabbits']
      });
    } catch (error) {
      console.error('Failed to load package:', error);
      showToast('Failed to load package data', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    if (errors[name]) setErrors(prev => { const err = { ...prev }; delete err[name]; return err; });
    setFormData(prev => ({
      ...prev,
      [name]: name === 'price' || name === 'deliveryFeePerKm' ? parseFloat(value) || 0 : value
    }));
  }, [errors]);

  // Handler functions
  const handleAddInclusion = useCallback(() => {
    if (!newInclusion.trim()) return;
    setFormData(prev => ({
      ...prev,
      inclusions: [...prev.inclusions, newInclusion.trim()]
    }));
    setNewInclusion('');
  }, [newInclusion]);

  const handleRemoveInclusion = useCallback((index: number) => {
    setFormData(prev => ({
      ...prev,
      inclusions: prev.inclusions.filter((_, i) => i !== index)
    }));
  }, []);

  const handleAddAddOn = useCallback(() => {
    if (!newAddOn.trim()) return;
    const price = newAddOnPrice ? parseFloat(newAddOnPrice) : null;
    setFormData(prev => ({
      ...prev,
      addOns: [...prev.addOns, { name: newAddOn.trim(), price }]
    }));
    setNewAddOn('');
    setNewAddOnPrice('');
  }, [newAddOn, newAddOnPrice]);

  const handleRemoveAddOn = useCallback((index: number) => {
    setFormData(prev => ({
      ...prev,
      addOns: prev.addOns.filter((_, i) => i !== index)
    }));
  }, []);

  const handleTogglePetType = useCallback((petType: string) => {
    setFormData(prev => {
      if (prev.supportedPetTypes.includes(petType)) {
        return {
          ...prev,
          supportedPetTypes: prev.supportedPetTypes.filter(type => type !== petType)
        };
      } else {
        return {
          ...prev,
          supportedPetTypes: [...prev.supportedPetTypes, petType]
        };
      }
    });
  }, []);

  // Image upload handlers
  const handleImageUpload = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    if (!file.type.startsWith('image/')) {
      setErrors(prev => ({ ...prev, images: 'Please select a valid image file' }));
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      setErrors(prev => ({ ...prev, images: 'Image size must be less than 5MB' }));
      return;
    }

    const imageId = Date.now().toString();
    setUploadingImages(prev => new Set([...prev, imageId]));

    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to upload image');
      }

      const result = await response.json();

      setFormData(prev => ({
        ...prev,
        images: [...prev.images, result.imagePath]
      }));

      // Clear any image errors
      if (errors.images) {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors.images;
          return newErrors;
        });
      }
    } catch (error) {
      console.error('Image upload failed:', error);
      setErrors(prev => ({ ...prev, images: 'Failed to upload image. Please try again.' }));
    } finally {
      setUploadingImages(prev => {
        const newSet = new Set(prev);
        newSet.delete(imageId);
        return newSet;
      });
    }
  }, [errors.images]);

  const handleRemoveImage = useCallback((index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }));
  }, []);

  const validateForm = useCallback(() => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = 'Package name is required';
    if (!formData.description.trim()) newErrors.description = 'Description is required';
    if (formData.price <= 0) newErrors.price = 'Price must be greater than zero';
    if (formData.inclusions.length === 0) newErrors.inclusions = 'At least one inclusion is required';
    if (!formData.conditions.trim()) newErrors.conditions = 'Conditions are required';
    if (formData.supportedPetTypes.length === 0) newErrors.supportedPetTypes = 'Please select at least one pet type';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      const url = mode === 'create' ? '/api/packages' : `/api/packages/${packageId}`;
      const method = mode === 'create' ? 'POST' : 'PATCH';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${mode} package`);
      }

      showToast(
        mode === 'create' ? 'Package created successfully!' : 'Package updated successfully!',
        'success'
      );

      handleSuccess();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : `Failed to ${mode} package`;
      setErrors({ submit: errorMessage });
      showToast(errorMessage, 'error');
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, mode, packageId, validateForm, showToast]);

  // Success animation handler
  const handleSuccess = () => {
    setShowSuccess(true);
    setTimeout(() => {
      setShowSuccess(false);
      onSuccess();
      onClose();
    }, 1500);
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="extra-large"
      className="max-h-[95vh] flex flex-col"
    >
      <div className="relative bg-white rounded-lg shadow-xl overflow-hidden">
        {/* Success overlay */}
        {showSuccess && (
          <div className="absolute inset-0 bg-white bg-opacity-95 flex items-center justify-center z-50 rounded-lg">
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-20 w-20 rounded-full bg-green-100 mb-6 animate-pulse">
                <CheckCircleIcon className="h-10 w-10 text-green-600 animate-bounce" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-3">
                {mode === 'create' ? 'Package Created!' : 'Package Updated!'}
              </h3>
              <p className="text-gray-600">
                {mode === 'create' ? 'Your new package has been created successfully.' : 'Your package has been updated successfully.'}
              </p>
            </div>
          </div>
        )}

        {/* Header */}
        <div className="bg-gradient-to-r from-[var(--primary-green)] to-green-600 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-white">
                {mode === 'create' ? 'Create New Package' : 'Edit Package'}
              </h2>
              <p className="text-green-100 text-sm mt-1">
                {mode === 'create' ? 'Add a new cremation package to your services' : 'Update your package details'}
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-green-100 transition-colors duration-200 p-2 rounded-full hover:bg-white/10"
              disabled={isSubmitting}
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto max-h-[calc(95vh-200px)]">
          {isLoading ? (
            <div className="flex items-center justify-center py-16">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-green)] mx-auto mb-4"></div>
                <span className="text-gray-600 text-lg">Loading package data...</span>
              </div>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-0">
              {/* Package Images - Top Section */}
              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 border-b border-gray-200">
                <div className="max-w-4xl mx-auto">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-800 mb-2">Package Images</h3>
                    <p className="text-gray-600">Upload high-quality images to showcase your package</p>
                  </div>

                  <div className="space-y-6">
                    {/* Upload Area */}
                    <div className="flex justify-center">
                      <div className="w-full max-w-md">
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleImageUpload}
                          className="hidden"
                          ref={fileInputRef}
                        />
                        <button
                          type="button"
                          onClick={() => fileInputRef.current?.click()}
                          className="w-full px-6 py-4 bg-white border-2 border-dashed border-gray-300 rounded-xl hover:border-[var(--primary-green)] hover:bg-green-50 transition-all duration-200 disabled:opacity-50 group"
                          disabled={uploadingImages.size > 0}
                        >
                          {uploadingImages.size > 0 ? (
                            <div className="flex items-center justify-center">
                              <svg className="animate-spin -ml-1 mr-3 h-6 w-6 text-[var(--primary-green)]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              <span className="text-[var(--primary-green)] font-medium">Uploading...</span>
                            </div>
                          ) : (
                            <div className="text-center">
                              <svg className="mx-auto h-12 w-12 text-gray-400 group-hover:text-[var(--primary-green)] transition-colors" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                              </svg>
                              <div className="mt-3">
                                <p className="text-lg font-medium text-gray-700 group-hover:text-[var(--primary-green)]">Upload Package Images</p>
                                <p className="text-sm text-gray-500 mt-1">PNG, JPG, GIF up to 5MB</p>
                              </div>
                            </div>
                          )}
                        </button>
                      </div>
                    </div>

                    {errors.images && (
                      <div className="text-center">
                        <p className="text-red-600 text-sm bg-red-50 px-4 py-2 rounded-lg inline-block">{errors.images}</p>
                      </div>
                    )}

                    {/* Image Gallery */}
                    {formData.images.length > 0 && (
                      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                        <h4 className="text-lg font-medium text-gray-800 mb-4">Uploaded Images ({formData.images.length})</h4>
                        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4">
                          {formData.images.map((image, index) => (
                            <div key={index} className="relative group">
                              <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 border-2 border-gray-200 hover:border-[var(--primary-green)] transition-colors">
                                <img
                                  src={image.startsWith('http') ? image : `${image}?t=${Date.now()}`}
                                  alt={`Package image ${index + 1}`}
                                  className="w-full h-full object-cover"
                                />
                              </div>
                              <button
                                type="button"
                                onClick={() => handleRemoveImage(index)}
                                className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1.5 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600 shadow-lg"
                              >
                                <XMarkIcon className="h-4 w-4" />
                              </button>
                              <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                                {index + 1}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Main Form Content */}
              <div className="p-6 space-y-8">
                {/* Basic Information */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                  <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-800">Basic Information</h3>
                    <p className="text-sm text-gray-600 mt-1">Essential details about your package</p>
                  </div>
                  <div className="p-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label htmlFor="name" className="block text-sm font-semibold text-gray-700">
                          Package Name*
                        </label>
                        <input
                          id="name"
                          name="name"
                          type="text"
                          value={formData.name}
                          onChange={handleInputChange}
                          className={`block w-full px-4 py-3 border-2 ${errors.name ? 'border-red-300 focus:border-red-500' : 'border-gray-200 focus:border-[var(--primary-green)]'} rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-[var(--primary-green)]/20 transition-all duration-200`}
                          placeholder="e.g., Premium Cremation Package"
                          required
                        />
                        {errors.name && <p className="text-sm text-red-600 flex items-center"><span className="mr-1">⚠️</span>{errors.name}</p>}
                      </div>

                      <div className="space-y-2">
                        <label htmlFor="category" className="block text-sm font-semibold text-gray-700">
                          Category*
                        </label>
                        <select
                          id="category"
                          name="category"
                          value={formData.category}
                          onChange={handleInputChange}
                          className={`block w-full px-4 py-3 border-2 ${errors.category ? 'border-red-300 focus:border-red-500' : 'border-gray-200 focus:border-[var(--primary-green)]'} rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-[var(--primary-green)]/20 transition-all duration-200`}
                          required
                        >
                          <option value="Private">Private Cremation</option>
                          <option value="Communal">Communal Cremation</option>
                          <option value="Individual">Individual Cremation</option>
                          <option value="Witnessed">Witnessed Cremation</option>
                        </select>
                        {errors.category && <p className="text-sm text-red-600 flex items-center"><span className="mr-1">⚠️</span>{errors.category}</p>}
                      </div>

                      <div className="space-y-2">
                        <label htmlFor="price" className="block text-sm font-semibold text-gray-700">
                          Base Price (₱)*
                        </label>
                        <div className="relative">
                          <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">₱</span>
                          <input
                            id="price"
                            name="price"
                            type="number"
                            value={formData.price || ''}
                            onChange={handleInputChange}
                            min="0"
                            step="any"
                            className={`block w-full pl-8 pr-4 py-3 border-2 ${errors.price ? 'border-red-300 focus:border-red-500' : 'border-gray-200 focus:border-[var(--primary-green)]'} rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-[var(--primary-green)]/20 transition-all duration-200`}
                            placeholder="3,500"
                            required
                          />
                        </div>
                        {errors.price && <p className="text-sm text-red-600 flex items-center"><span className="mr-1">⚠️</span>{errors.price}</p>}
                      </div>

                      <div className="space-y-2">
                        <label htmlFor="pricePerKg" className="block text-sm font-semibold text-gray-700">
                          Price Per Kg (₱)
                        </label>
                        <div className="relative">
                          <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">₱</span>
                          <input
                            id="pricePerKg"
                            name="pricePerKg"
                            type="number"
                            value={formData.pricePerKg || ''}
                            onChange={handleInputChange}
                            min="0"
                            step="any"
                            className={`block w-full pl-8 pr-4 py-3 border-2 ${errors.pricePerKg ? 'border-red-300 focus:border-red-500' : 'border-gray-200 focus:border-[var(--primary-green)]'} rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-[var(--primary-green)]/20 transition-all duration-200`}
                            placeholder="100"
                          />
                        </div>
                        {errors.pricePerKg && <p className="text-sm text-red-600 flex items-center"><span className="mr-1">⚠️</span>{errors.pricePerKg}</p>}
                        <p className="text-xs text-gray-500 bg-blue-50 px-3 py-2 rounded-md">💡 Additional charge per kg of pet weight</p>
                      </div>

                      <div className="lg:col-span-2 space-y-2">
                        <label htmlFor="description" className="block text-sm font-semibold text-gray-700">
                          Description*
                        </label>
                        <textarea
                          id="description"
                          name="description"
                          rows={4}
                          value={formData.description}
                          onChange={handleInputChange}
                          className={`block w-full px-4 py-3 border-2 ${errors.description ? 'border-red-300 focus:border-red-500' : 'border-gray-200 focus:border-[var(--primary-green)]'} rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-[var(--primary-green)]/20 transition-all duration-200 resize-none`}
                          placeholder="Describe your cremation package, what's included, and what makes it special..."
                          required
                        />
                        {errors.description && <p className="text-sm text-red-600 flex items-center"><span className="mr-1">⚠️</span>{errors.description}</p>}
                      </div>
                </div>

                <div className="mt-4">
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Description*
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    rows={3}
                    value={formData.description}
                    onChange={handleInputChange}
                    className={`block w-full px-3 py-2 border ${errors.description ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-green)] focus:border-[var(--primary-green)] sm:text-sm`}
                    placeholder="Describe your package in detail"
                    required
                  />
                  {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
                </div>
              </div>

                {/* Service Details */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                  <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-800">Service Details</h3>
                    <p className="text-sm text-gray-600 mt-1">Specify the type and timing of your cremation service</p>
                  </div>
                  <div className="p-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label htmlFor="cremationType" className="block text-sm font-semibold text-gray-700">
                          Cremation Type*
                        </label>
                        <select
                          id="cremationType"
                          name="cremationType"
                          value={formData.cremationType}
                          onChange={handleInputChange}
                          className={`block w-full px-4 py-3 border-2 ${errors.cremationType ? 'border-red-300 focus:border-red-500' : 'border-gray-200 focus:border-[var(--primary-green)]'} rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-[var(--primary-green)]/20 transition-all duration-200`}
                          required
                        >
                          <option value="Standard">Standard Cremation</option>
                          <option value="Premium">Premium Cremation</option>
                          <option value="Eco-Friendly">Eco-Friendly Cremation</option>
                          <option value="Express">Express Cremation</option>
                        </select>
                        {errors.cremationType && <p className="text-sm text-red-600 flex items-center"><span className="mr-1">⚠️</span>{errors.cremationType}</p>}
                      </div>

                      <div className="space-y-2">
                        <label htmlFor="processingTime" className="block text-sm font-semibold text-gray-700">
                          Processing Time*
                        </label>
                        <select
                          id="processingTime"
                          name="processingTime"
                          value={formData.processingTime}
                          onChange={handleInputChange}
                          className={`block w-full px-4 py-3 border-2 ${errors.processingTime ? 'border-red-300 focus:border-red-500' : 'border-gray-200 focus:border-[var(--primary-green)]'} rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-[var(--primary-green)]/20 transition-all duration-200`}
                          required
                        >
                          <option value="1-2 days">1-2 days</option>
                          <option value="3-5 days">3-5 days</option>
                          <option value="1 week">1 week</option>
                          <option value="2 weeks">2 weeks</option>
                        </select>
                        {errors.processingTime && <p className="text-sm text-red-600 flex items-center"><span className="mr-1">⚠️</span>{errors.processingTime}</p>}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Inclusions */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                  <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-800">Package Inclusions*</h3>
                    <p className="text-sm text-gray-600 mt-1">What's included in this package</p>
                  </div>
                  <div className="p-6">
                    <div className="flex gap-3 mb-4">
                      <input
                        type="text"
                        value={newInclusion}
                        onChange={(e) => setNewInclusion(e.target.value)}
                        className="flex-grow px-4 py-3 border-2 border-gray-200 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-[var(--primary-green)]/20 focus:border-[var(--primary-green)] transition-all duration-200"
                        placeholder="e.g., Standard clay urn, Certificate of cremation"
                        onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddInclusion())}
                      />
                      <button
                        type="button"
                        onClick={handleAddInclusion}
                        className="px-6 py-3 bg-[var(--primary-green)] text-white rounded-lg hover:bg-[var(--primary-green-hover)] transition-all duration-200 font-medium shadow-sm hover:shadow-md"
                      >
                        Add
                      </button>
                    </div>
                    {formData.inclusions.length > 0 && (
                      <div className="space-y-3">
                        <h4 className="text-sm font-medium text-gray-700">Included Items ({formData.inclusions.length})</h4>
                        <div className="grid gap-2">
                          {formData.inclusions.map((inclusion, index) => (
                            <div key={index} className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200 group hover:bg-green-100 transition-colors">
                              <div className="flex items-center">
                                <span className="text-green-600 mr-2">✓</span>
                                <span className="text-sm font-medium text-gray-800">{inclusion}</span>
                              </div>
                              <button
                                type="button"
                                onClick={() => handleRemoveInclusion(index)}
                                className="text-red-500 hover:text-red-700 opacity-0 group-hover:opacity-100 transition-all duration-200 p-1 rounded"
                              >
                                <XMarkIcon className="h-4 w-4" />
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                    {errors.inclusions && <p className="mt-3 text-sm text-red-600 flex items-center"><span className="mr-1">⚠️</span>{errors.inclusions}</p>}
                  </div>
                </div>

                {/* Add-ons */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                  <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-800">Optional Add-ons</h3>
                    <p className="text-sm text-gray-600 mt-1">Additional services customers can purchase</p>
                  </div>
                  <div className="p-6">
                    <div className="flex gap-3 mb-4">
                      <input
                        type="text"
                        value={newAddOn}
                        onChange={(e) => setNewAddOn(e.target.value)}
                        className="flex-grow px-4 py-3 border-2 border-gray-200 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-[var(--primary-green)]/20 focus:border-[var(--primary-green)] transition-all duration-200"
                        placeholder="e.g., Personalized nameplate, Memorial photo frame"
                        onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddAddOn())}
                      />
                      <div className="w-36">
                        <div className="relative">
                          <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">₱</span>
                          <input
                            type="number"
                            min="0"
                            step="0.01"
                            value={newAddOnPrice}
                            onChange={(e) => setNewAddOnPrice(e.target.value)}
                            placeholder="Price"
                            className="w-full pl-8 pr-4 py-3 border-2 border-gray-200 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-[var(--primary-green)]/20 focus:border-[var(--primary-green)] transition-all duration-200"
                          />
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={handleAddAddOn}
                        className="px-6 py-3 bg-[var(--primary-green)] text-white rounded-lg hover:bg-[var(--primary-green-hover)] transition-all duration-200 font-medium shadow-sm hover:shadow-md"
                      >
                        Add
                      </button>
                    </div>
                    {formData.addOns.length > 0 && (
                      <div className="space-y-3">
                        <h4 className="text-sm font-medium text-gray-700">Available Add-ons ({formData.addOns.length})</h4>
                        <div className="grid gap-2">
                          {formData.addOns.map((addOn, index) => (
                            <div key={index} className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200 group hover:bg-blue-100 transition-colors">
                              <div className="flex items-center">
                                <span className="text-blue-600 mr-2">+</span>
                                <span className="text-sm font-medium text-gray-800">
                                  {addOn.name} {addOn.price && <span className="text-blue-600 font-semibold">(+₱{addOn.price.toLocaleString()})</span>}
                                </span>
                              </div>
                              <button
                                type="button"
                                onClick={() => handleRemoveAddOn(index)}
                                className="text-red-500 hover:text-red-700 opacity-0 group-hover:opacity-100 transition-all duration-200 p-1 rounded"
                              >
                                <XMarkIcon className="h-4 w-4" />
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

              {/* Supported Pet Types */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-800 mb-4">Supported Pet Types*</h3>
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                  {['Dogs', 'Cats', 'Birds', 'Rabbits', 'Hamsters', 'Guinea Pigs', 'Ferrets', 'Other Small Pets'].map((petType) => (
                    <label key={petType} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.supportedPetTypes.includes(petType)}
                        onChange={() => handleTogglePetType(petType)}
                        className="h-4 w-4 text-[var(--primary-green)] focus:ring-[var(--primary-green)] border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">{petType}</span>
                    </label>
                  ))}
                </div>
                {errors.supportedPetTypes && <p className="mt-2 text-sm text-red-600">{errors.supportedPetTypes}</p>}
              </div>

              {/* Package Images */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-800 mb-4">Package Images</h3>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                      ref={fileInputRef}
                    />
                    <button
                      type="button"
                      onClick={() => fileInputRef.current?.click()}
                      className="px-4 py-2 bg-[var(--primary-green)] text-white rounded-md hover:bg-[var(--primary-green-hover)] disabled:opacity-50"
                      disabled={uploadingImages.size > 0}
                    >
                      {uploadingImages.size > 0 ? (
                        <span className="flex items-center">
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Uploading...
                        </span>
                      ) : (
                        'Upload Image'
                      )}
                    </button>
                    <span className="ml-3 text-sm text-gray-500">Upload package images (max 5MB)</span>
                  </div>

                  {errors.images && <p className="text-sm text-red-600">{errors.images}</p>}

                  {formData.images.length > 0 && (
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mt-4">
                      {formData.images.map((image, index) => (
                        <div key={index} className="relative group">
                          <img
                            src={image.startsWith('http') ? image : `${image}?t=${Date.now()}`}
                            alt={`Package image ${index + 1}`}
                            className="h-32 w-full object-cover rounded-md border border-gray-200"
                          />
                          <button
                            type="button"
                            onClick={() => handleRemoveImage(index)}
                            className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <XMarkIcon className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

                {/* Conditions */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                  <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-800">Terms & Conditions*</h3>
                    <p className="text-sm text-gray-600 mt-1">Important conditions and restrictions for this package</p>
                  </div>
                  <div className="p-6">
                    <textarea
                      id="conditions"
                      name="conditions"
                      rows={4}
                      value={formData.conditions}
                      onChange={handleInputChange}
                      className={`block w-full px-4 py-3 border-2 ${errors.conditions ? 'border-red-300 focus:border-red-500' : 'border-gray-200 focus:border-[var(--primary-green)]'} rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-[var(--primary-green)]/20 transition-all duration-200 resize-none`}
                      placeholder="e.g., For pets up to 50 kg. Additional fees may apply for larger pets. Ashes will be ready for pickup within the specified processing time..."
                      required
                    />
                    {errors.conditions && <p className="mt-3 text-sm text-red-600 flex items-center"><span className="mr-1">⚠️</span>{errors.conditions}</p>}
                  </div>
                </div>

                {/* Error display */}
                {errors.submit && (
                  <div className="bg-red-50 border-2 border-red-200 rounded-xl p-4 mx-6">
                    <div className="flex items-center">
                      <span className="text-red-500 mr-2">⚠️</span>
                      <p className="text-sm text-red-700 font-medium">{errors.submit}</p>
                    </div>
                  </div>
                )}
            </form>
          )}
        </div>

        {/* Footer */}
        {!isLoading && (
          <div className="bg-white border-t border-gray-200 px-6 py-4">
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-500">
                {mode === 'create' ? 'Create a new package for your cremation services' : 'Update your package details'}
              </div>
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={onClose}
                  disabled={isSubmitting}
                  className="px-6 py-3 border-2 border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 transition-all duration-200 font-medium"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="px-8 py-3 bg-gradient-to-r from-[var(--primary-green)] to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 disabled:opacity-50 flex items-center transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl font-medium"
                >
                  {isSubmitting && (
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                  )}
                  {isSubmitting
                    ? (mode === 'create' ? 'Creating Package...' : 'Updating Package...')
                    : (mode === 'create' ? '✨ Create Package' : '💾 Update Package')
                  }
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default PackageModal;
