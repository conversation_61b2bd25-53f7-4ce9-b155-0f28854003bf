'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Modal } from '@/components/ui';
import { XMarkIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import { useToast } from '@/context/ToastContext';
import { ToastType } from '@/types/toast';

// Types
interface AddOn {
  name: string;
  price: number | null;
}

interface PackageFormData {
  name: string;
  description: string;
  category: string;
  cremationType: string;
  processingTime: string;
  price: number;
  deliveryFeePerKm: number;
  inclusions: string[];
  addOns: AddOn[];
  conditions: string;
  images: string[];
  packageId?: number;
  // Enhanced features
  pricePerKg: number;
  usesCustomOptions: boolean;
  customCategories: string[];
  customCremationTypes: string[];
  customProcessingTimes: string[];
  supportedPetTypes: string[];
}

interface PackageModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  mode: 'create' | 'edit';
  packageId?: number;
  initialData?: Partial<PackageFormData>;
}

const PackageModal: React.FC<PackageModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  mode,
  packageId,
  initialData
}) => {
  const { showToast } = useToast();
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  
  // Form state
  const [formData, setFormData] = useState<PackageFormData>({
    name: '',
    description: '',
    category: 'Private',
    cremationType: 'Standard',
    processingTime: '1-2 days',
    price: 0,
    deliveryFeePerKm: 0,
    inclusions: [],
    addOns: [],
    conditions: '',
    images: [],
    packageId: packageId,
    // Enhanced features
    pricePerKg: 0,
    usesCustomOptions: false,
    customCategories: [],
    customCremationTypes: [],
    customProcessingTimes: [],
    supportedPetTypes: ['Dogs', 'Cats', 'Birds', 'Rabbits']
  });

  // UI state
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  
  // Form field states
  const [newInclusion, setNewInclusion] = useState('');
  const [newAddOn, setNewAddOn] = useState('');
  const [newAddOnPrice, setNewAddOnPrice] = useState<string>('');
  const [uploadingImages, setUploadingImages] = useState<Set<string>>(new Set());
  
  // Enhanced features states
  const [newCustomCategory, setNewCustomCategory] = useState('');
  const [newCustomCremationType, setNewCustomCremationType] = useState('');
  const [newCustomProcessingTime, setNewCustomProcessingTime] = useState('');

  // Load package data for edit mode
  useEffect(() => {
    if (mode === 'edit' && packageId && isOpen) {
      loadPackageData();
    } else if (mode === 'create' && isOpen) {
      resetForm();
    }
  }, [mode, packageId, isOpen]);

  // Apply initial data if provided
  useEffect(() => {
    if (initialData && isOpen) {
      setFormData(prev => ({ ...prev, ...initialData }));
    }
  }, [initialData, isOpen]);

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      category: 'Private',
      cremationType: 'Standard',
      processingTime: '1-2 days',
      price: 0,
      deliveryFeePerKm: 0,
      inclusions: [],
      addOns: [],
      conditions: '',
      images: [],
      packageId: undefined,
      pricePerKg: 0,
      usesCustomOptions: false,
      customCategories: [],
      customCremationTypes: [],
      customProcessingTimes: [],
      supportedPetTypes: ['Dogs', 'Cats', 'Birds', 'Rabbits']
    });
    setErrors({});
    setNewInclusion('');
    setNewAddOn('');
    setNewAddOnPrice('');
    setNewCustomCategory('');
    setNewCustomCremationType('');
    setNewCustomProcessingTime('');
  };

  const loadPackageData = async () => {
    if (!packageId) return;
    
    setIsLoading(true);
    try {
      const response = await fetch(`/api/packages/${packageId}`, {
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error('Failed to load package data');
      }
      
      const data = await response.json();
      const pkg = data.package;
      
      setFormData({
        name: pkg.name || '',
        description: pkg.description || '',
        category: pkg.category || 'Private',
        cremationType: pkg.cremationType || 'Standard',
        processingTime: pkg.processingTime || '1-2 days',
        price: pkg.price || 0,
        deliveryFeePerKm: pkg.deliveryFeePerKm || 0,
        inclusions: pkg.inclusions || [],
        addOns: pkg.addOns || [],
        conditions: pkg.conditions || '',
        images: pkg.images || [],
        packageId: pkg.id,
        pricePerKg: pkg.pricePerKg || 0,
        usesCustomOptions: pkg.usesCustomOptions || false,
        customCategories: pkg.customCategories || [],
        customCremationTypes: pkg.customCremationTypes || [],
        customProcessingTimes: pkg.customProcessingTimes || [],
        supportedPetTypes: pkg.supportedPetTypes || ['Dogs', 'Cats', 'Birds', 'Rabbits']
      });
    } catch (error) {
      console.error('Failed to load package:', error);
      showToast('Failed to load package data', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    if (errors[name]) setErrors(prev => { const err = { ...prev }; delete err[name]; return err; });
    setFormData(prev => ({
      ...prev,
      [name]: name === 'price' || name === 'deliveryFeePerKm' ? parseFloat(value) || 0 : value
    }));
  }, [errors]);

  // Handler functions
  const handleAddInclusion = useCallback(() => {
    if (!newInclusion.trim()) return;
    setFormData(prev => ({
      ...prev,
      inclusions: [...prev.inclusions, newInclusion.trim()]
    }));
    setNewInclusion('');
  }, [newInclusion]);

  const handleRemoveInclusion = useCallback((index: number) => {
    setFormData(prev => ({
      ...prev,
      inclusions: prev.inclusions.filter((_, i) => i !== index)
    }));
  }, []);

  const handleAddAddOn = useCallback(() => {
    if (!newAddOn.trim()) return;
    const price = newAddOnPrice ? parseFloat(newAddOnPrice) : null;
    setFormData(prev => ({
      ...prev,
      addOns: [...prev.addOns, { name: newAddOn.trim(), price }]
    }));
    setNewAddOn('');
    setNewAddOnPrice('');
  }, [newAddOn, newAddOnPrice]);

  const handleRemoveAddOn = useCallback((index: number) => {
    setFormData(prev => ({
      ...prev,
      addOns: prev.addOns.filter((_, i) => i !== index)
    }));
  }, []);

  const handleTogglePetType = useCallback((petType: string) => {
    setFormData(prev => {
      if (prev.supportedPetTypes.includes(petType)) {
        return {
          ...prev,
          supportedPetTypes: prev.supportedPetTypes.filter(type => type !== petType)
        };
      } else {
        return {
          ...prev,
          supportedPetTypes: [...prev.supportedPetTypes, petType]
        };
      }
    });
  }, []);

  // Image upload handlers
  const handleImageUpload = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    if (!file.type.startsWith('image/')) {
      setErrors(prev => ({ ...prev, images: 'Please select a valid image file' }));
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      setErrors(prev => ({ ...prev, images: 'Image size must be less than 5MB' }));
      return;
    }

    const imageId = Date.now().toString();
    setUploadingImages(prev => new Set([...prev, imageId]));

    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to upload image');
      }

      const result = await response.json();

      setFormData(prev => ({
        ...prev,
        images: [...prev.images, result.imagePath]
      }));

      // Clear any image errors
      if (errors.images) {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors.images;
          return newErrors;
        });
      }
    } catch (error) {
      console.error('Image upload failed:', error);
      setErrors(prev => ({ ...prev, images: 'Failed to upload image. Please try again.' }));
    } finally {
      setUploadingImages(prev => {
        const newSet = new Set(prev);
        newSet.delete(imageId);
        return newSet;
      });
    }
  }, [errors.images]);

  const handleRemoveImage = useCallback((index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }));
  }, []);

  const validateForm = useCallback(() => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = 'Package name is required';
    if (!formData.description.trim()) newErrors.description = 'Description is required';
    if (formData.price <= 0) newErrors.price = 'Price must be greater than zero';
    if (formData.inclusions.length === 0) newErrors.inclusions = 'At least one inclusion is required';
    if (!formData.conditions.trim()) newErrors.conditions = 'Conditions are required';
    if (formData.supportedPetTypes.length === 0) newErrors.supportedPetTypes = 'Please select at least one pet type';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      const url = mode === 'create' ? '/api/packages' : `/api/packages/${packageId}`;
      const method = mode === 'create' ? 'POST' : 'PATCH';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${mode} package`);
      }

      showToast(
        mode === 'create' ? 'Package created successfully!' : 'Package updated successfully!',
        'success'
      );

      handleSuccess();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : `Failed to ${mode} package`;
      setErrors({ submit: errorMessage });
      showToast(errorMessage, 'error');
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, mode, packageId, validateForm, showToast]);

  // Success animation handler
  const handleSuccess = () => {
    setShowSuccess(true);
    setTimeout(() => {
      setShowSuccess(false);
      onSuccess();
      onClose();
    }, 1500);
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="extra-large"
      className="max-h-[95vh] flex flex-col"
    >
      <div className="relative">
        {/* Success overlay */}
        {showSuccess && (
          <div className="absolute inset-0 bg-white bg-opacity-95 flex items-center justify-center z-50 rounded-lg">
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                <CheckCircleIcon className="h-8 w-8 text-green-600 animate-bounce" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {mode === 'create' ? 'Package Created!' : 'Package Updated!'}
              </h3>
              <p className="text-sm text-gray-500">
                {mode === 'create' ? 'Your new package has been created successfully.' : 'Your package has been updated successfully.'}
              </p>
            </div>
          </div>
        )}

        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {mode === 'create' ? 'Create New Package' : 'Edit Package'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
            disabled={isSubmitting}
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 flex-1 overflow-y-auto">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-green)]"></div>
              <span className="ml-3 text-gray-600">Loading package data...</span>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-800 mb-4">Basic Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                      Package Name*
                    </label>
                    <input
                      id="name"
                      name="name"
                      type="text"
                      value={formData.name}
                      onChange={handleInputChange}
                      className={`block w-full px-3 py-2 border ${errors.name ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-green)] focus:border-[var(--primary-green)] sm:text-sm`}
                      placeholder="e.g., Basic Cremation"
                      required
                    />
                    {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
                  </div>

                  <div>
                    <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">
                      Base Price (₱)*
                    </label>
                    <input
                      id="price"
                      name="price"
                      type="number"
                      value={formData.price || ''}
                      onChange={handleInputChange}
                      min="0"
                      step="any"
                      className={`block w-full px-3 py-2 border ${errors.price ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-green)] focus:border-[var(--primary-green)] sm:text-sm`}
                      placeholder="e.g., 3500"
                      required
                    />
                    {errors.price && <p className="mt-1 text-sm text-red-600">{errors.price}</p>}
                  </div>

                  <div>
                    <label htmlFor="pricePerKg" className="block text-sm font-medium text-gray-700 mb-1">
                      Price Per Kg (₱)
                    </label>
                    <input
                      id="pricePerKg"
                      name="pricePerKg"
                      type="number"
                      value={formData.pricePerKg || ''}
                      onChange={handleInputChange}
                      min="0"
                      step="any"
                      className={`block w-full px-3 py-2 border ${errors.pricePerKg ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-green)] focus:border-[var(--primary-green)] sm:text-sm`}
                      placeholder="e.g., 100"
                    />
                    {errors.pricePerKg && <p className="mt-1 text-sm text-red-600">{errors.pricePerKg}</p>}
                    <p className="mt-1 text-xs text-gray-500">Additional charge per kg of pet weight</p>
                  </div>
                </div>

                <div className="mt-4">
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Description*
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    rows={3}
                    value={formData.description}
                    onChange={handleInputChange}
                    className={`block w-full px-3 py-2 border ${errors.description ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-green)] focus:border-[var(--primary-green)] sm:text-sm`}
                    placeholder="Describe your package in detail"
                    required
                  />
                  {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
                </div>
              </div>

              {/* Package Details */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-800 mb-4">Package Details</h3>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div>
                    <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                      Category
                    </label>
                    <select
                      id="category"
                      name="category"
                      value={formData.category}
                      onChange={handleInputChange}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-green)] focus:border-[var(--primary-green)] sm:text-sm"
                    >
                      <option value="Private">Private</option>
                      <option value="Communal">Communal</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="cremationType" className="block text-sm font-medium text-gray-700 mb-1">
                      Cremation Type
                    </label>
                    <select
                      id="cremationType"
                      name="cremationType"
                      value={formData.cremationType}
                      onChange={handleInputChange}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-green)] focus:border-[var(--primary-green)] sm:text-sm"
                    >
                      <option value="Standard">Standard</option>
                      <option value="Premium">Premium</option>
                      <option value="Deluxe">Deluxe</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="processingTime" className="block text-sm font-medium text-gray-700 mb-1">
                      Processing Time
                    </label>
                    <select
                      id="processingTime"
                      name="processingTime"
                      value={formData.processingTime}
                      onChange={handleInputChange}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-green)] focus:border-[var(--primary-green)] sm:text-sm"
                    >
                      <option value="Same day">Same day</option>
                      <option value="1-2 days">1-2 days</option>
                      <option value="2-3 days">2-3 days</option>
                      <option value="3-5 days">3-5 days</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Inclusions */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-800 mb-4">Inclusions*</h3>
                <div className="flex gap-2 mb-3">
                  <input
                    type="text"
                    value={newInclusion}
                    onChange={(e) => setNewInclusion(e.target.value)}
                    className="flex-grow px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-green)] focus:border-[var(--primary-green)] sm:text-sm"
                    placeholder="e.g., Standard clay urn"
                    onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddInclusion())}
                  />
                  <button
                    type="button"
                    onClick={handleAddInclusion}
                    className="px-4 py-2 bg-[var(--primary-green)] text-white rounded-md hover:bg-[var(--primary-green-hover)]"
                  >
                    Add
                  </button>
                </div>
                {formData.inclusions.length > 0 && (
                  <div className="space-y-2">
                    {formData.inclusions.map((inclusion, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-white rounded border">
                        <span className="text-sm">{inclusion}</span>
                        <button
                          type="button"
                          onClick={() => handleRemoveInclusion(index)}
                          className="text-red-600 hover:text-red-800 text-sm"
                        >
                          Remove
                        </button>
                      </div>
                    ))}
                  </div>
                )}
                {errors.inclusions && <p className="mt-2 text-sm text-red-600">{errors.inclusions}</p>}
              </div>

              {/* Add-ons */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-800 mb-4">Add-ons (Optional)</h3>
                <div className="flex gap-2 mb-3">
                  <input
                    type="text"
                    value={newAddOn}
                    onChange={(e) => setNewAddOn(e.target.value)}
                    className="flex-grow px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-green)] focus:border-[var(--primary-green)] sm:text-sm"
                    placeholder="e.g., Personalized nameplate"
                    onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddAddOn())}
                  />
                  <div className="w-32">
                    <div className="flex items-center border border-gray-300 rounded-md shadow-sm px-3 py-2">
                      <span className="text-gray-500 mr-1">₱</span>
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        value={newAddOnPrice}
                        onChange={(e) => setNewAddOnPrice(e.target.value)}
                        placeholder="Price"
                        className="w-full focus:outline-none sm:text-sm"
                      />
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={handleAddAddOn}
                    className="px-4 py-2 bg-[var(--primary-green)] text-white rounded-md hover:bg-[var(--primary-green-hover)]"
                  >
                    Add
                  </button>
                </div>
                {formData.addOns.length > 0 && (
                  <div className="space-y-2">
                    {formData.addOns.map((addOn, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-white rounded border">
                        <span className="text-sm">
                          {addOn.name} {addOn.price && `(+₱${addOn.price.toLocaleString()})`}
                        </span>
                        <button
                          type="button"
                          onClick={() => handleRemoveAddOn(index)}
                          className="text-red-600 hover:text-red-800 text-sm"
                        >
                          Remove
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Supported Pet Types */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-800 mb-4">Supported Pet Types*</h3>
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                  {['Dogs', 'Cats', 'Birds', 'Rabbits', 'Hamsters', 'Guinea Pigs', 'Ferrets', 'Other Small Pets'].map((petType) => (
                    <label key={petType} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.supportedPetTypes.includes(petType)}
                        onChange={() => handleTogglePetType(petType)}
                        className="h-4 w-4 text-[var(--primary-green)] focus:ring-[var(--primary-green)] border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">{petType}</span>
                    </label>
                  ))}
                </div>
                {errors.supportedPetTypes && <p className="mt-2 text-sm text-red-600">{errors.supportedPetTypes}</p>}
              </div>

              {/* Package Images */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-800 mb-4">Package Images</h3>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                      ref={fileInputRef}
                    />
                    <button
                      type="button"
                      onClick={() => fileInputRef.current?.click()}
                      className="px-4 py-2 bg-[var(--primary-green)] text-white rounded-md hover:bg-[var(--primary-green-hover)] disabled:opacity-50"
                      disabled={uploadingImages.size > 0}
                    >
                      {uploadingImages.size > 0 ? (
                        <span className="flex items-center">
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Uploading...
                        </span>
                      ) : (
                        'Upload Image'
                      )}
                    </button>
                    <span className="ml-3 text-sm text-gray-500">Upload package images (max 5MB)</span>
                  </div>

                  {errors.images && <p className="text-sm text-red-600">{errors.images}</p>}

                  {formData.images.length > 0 && (
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mt-4">
                      {formData.images.map((image, index) => (
                        <div key={index} className="relative group">
                          <img
                            src={image.startsWith('http') ? image : `${image}?t=${Date.now()}`}
                            alt={`Package image ${index + 1}`}
                            className="h-32 w-full object-cover rounded-md border border-gray-200"
                          />
                          <button
                            type="button"
                            onClick={() => handleRemoveImage(index)}
                            className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <XMarkIcon className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Conditions */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-800 mb-4">Conditions and Restrictions*</h3>
                <textarea
                  id="conditions"
                  name="conditions"
                  rows={3}
                  value={formData.conditions}
                  onChange={handleInputChange}
                  className={`block w-full px-3 py-2 border ${errors.conditions ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-green)] focus:border-[var(--primary-green)] sm:text-sm`}
                  placeholder="e.g., For pets up to 50 kg. Additional fees may apply for larger pets."
                  required
                />
                {errors.conditions && <p className="mt-2 text-sm text-red-600">{errors.conditions}</p>}
              </div>

              {/* Error display */}
              {errors.submit && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4">
                  <p className="text-sm text-red-600">{errors.submit}</p>
                </div>
              )}
            </form>
          )}
        </div>

        {/* Footer */}
        {!isLoading && (
          <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
            <button
              type="button"
              onClick={onClose}
              disabled={isSubmitting}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="px-4 py-2 bg-[var(--primary-green)] text-white rounded-md hover:bg-[var(--primary-green-hover)] disabled:opacity-50 flex items-center transition-all duration-200 transform hover:scale-105"
            >
              {isSubmitting && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              )}
              {isSubmitting
                ? (mode === 'create' ? 'Creating...' : 'Updating...')
                : (mode === 'create' ? 'Create Package' : 'Update Package')
              }
            </button>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default PackageModal;
